package com.knet.user.mq.consumer;

import com.alibaba.fastjson2.JSON;
import com.knet.common.dto.UserOperationRecordMessage;
import com.knet.user.model.dto.req.CreateUserOperationRecordRequest;
import com.knet.user.service.ISysUserOperationRecordService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2025/5/20 20:30
 * @description: 用户操作记录消费者
 */
@Slf4j
@Component
public class UserOperationRecordConsumer {
    @Resource
    private ISysUserOperationRecordService sysUserOperationRecordService;

    /**
     * 消费用户操作记录消息
     *
     * @param message 消息内容
     * @param channel 通道
     */
    @RabbitListener(queues = "user-operation-queue.user-services")
    public void consumeUserOperationRecord(Message message, Channel channel) {
        String messageBody = new String(message.getBody());
        String messageId = (String) message.getMessageProperties().getHeaders().get("messageId");
        Long userId = (Long) message.getMessageProperties().getHeaders().get("userId");
        log.info("接收到用户操作记录消息: messageId={}, userId={}, body={}", messageId, userId, messageBody);
        try {
            UserOperationRecordMessage recordMessage = JSON.parseObject(messageBody, UserOperationRecordMessage.class);
            CreateUserOperationRecordRequest request = CreateUserOperationRecordRequest.builder()
                    .userId(recordMessage.getUserId())
                    .operatorId(recordMessage.getOperatorId())
                    .operatorType(recordMessage.getOperatorType())
                    .operationType(recordMessage.getOperationType())
                    .operationResult(recordMessage.getOperationResult())
                    .operationDesc(recordMessage.getOperationDesc())
                    .operationDetail(recordMessage.getOperationDetail())
                    .clientIp(recordMessage.getClientIp())
                    .userAgent(recordMessage.getUserAgent())
                    .businessId(recordMessage.getBusinessId())
                    .businessType(recordMessage.getBusinessType())
                    .errorMessage(recordMessage.getErrorMessage())
                    .remarks(recordMessage.getRemarks())
                    .build();
            String operationId = sysUserOperationRecordService.createOperationRecord(request);
            log.info("用户操作记录处理成功: messageId={}, operationId={}, userId={}", messageId, operationId, userId);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("用户操作记录处理失败: messageId={}, userId={}, error={}",
                    messageId, userId, e.getMessage(), e);
            try {
                // 获取重试次数
                Integer retryCount = (Integer) message.getMessageProperties().getHeaders().get("x-retry-count");
                if (retryCount == null) {
                    retryCount = 0;
                }
                // 最大重试3次
                if (retryCount < 3) {
                    log.info("消息重试: messageId={}, retryCount={}", messageId, retryCount + 1);
                    // 拒绝消息，重新入队
                    channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, true);
                } else {
                    log.error("消息重试次数超限，发送到死信队列: messageId={}", messageId);
                    // 拒绝消息，不重新入队（发送到死信队列）
                    channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
                }
            } catch (IOException ioException) {
                log.error("消息确认失败: messageId={}, error={}", messageId, ioException.getMessage());
            }
        }
    }
}

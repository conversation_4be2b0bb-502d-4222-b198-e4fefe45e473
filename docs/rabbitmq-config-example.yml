# RabbitMQ配置示例
# 在application.yml中添加以下配置

spring:
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest
    virtual-host: /
    # 发送确认
    publisher-confirm-type: correlated
    # 发送失败退回
    publisher-returns: true
    template:
      # 必须设置为true，消息路由失败时才会回调
      mandatory: true
    listener:
      simple:
        # 手动确认消息
        acknowledge-mode: manual
        # 每次从队列中取几个消息
        prefetch: 1
        # 重试机制
        retry:
          enabled: true
          initial-interval: 1000ms
          max-attempts: 3
          max-interval: 10000ms
          multiplier: 1.0

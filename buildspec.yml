version: 0.2

phases:
  install:
    runtime-versions:
      java: corretto17
  pre_build:
    commands:
      - echo Installing Maven
      - mvn -version
  build:
    commands:
      - echo Build started on `date`
      - mvn clean install -DskipTests
      - echo Build completed on `date`
      - mvn package install

      # Create directories for staging and artifacts
      - mkdir -p staging/lib
      - mkdir -p artifacts

      # Copy dependency libraries
      # - cp target/lib/*.jar staging/lib

      # Copy built jars from specific project modules to staging
      - cp gateway/target/gateway-*.jar  staging/
      - cp user-services/target/user-services-*.jar  staging/
      - cp goods-services/target/goods-services-*.jar  staging/

      # Package all files in the staging directory into a gzipped tar archive
      - cd staging
      - tar cvfz ../artifacts/jars.tar.gz lib/ *.jar
      - cd ..

  post_build:
    commands:
    # - echo Copying all necessary files for deployment
    # - cp appspec.yml artifacts/
    # - cp -r scripts/ artifacts/scripts/
artifacts:
  name: knet-b2b-$(date +%Y%m%d%H%M%S)
  files:
    - artifacts/jars.tar.gz
    - appspec.yml
    - codedeploy-scripts/*.sh
  discard-paths: yes
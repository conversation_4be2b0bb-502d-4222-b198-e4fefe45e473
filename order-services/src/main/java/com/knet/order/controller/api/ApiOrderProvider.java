package com.knet.order.controller.api;

import com.knet.common.base.HttpResult;
import com.knet.order.mq.producer.OrderProducer;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/3/11 16:51
 * @description: 订单对外提供服务
 */
@Slf4j
@RestController
@RequestMapping("/api")
@Tag(name = "订单服务-对外提供接口", description = "订单服务-对外提供接口")
public class ApiOrderProvider {
    @Resource
    private OrderProducer orderProducer;

    @GetMapping("/send")
    public HttpResult<Void> send() {
        orderProducer.sendOrderCreateEvent("hello order");
        return HttpResult.ok();
    }
}

package com.knet.order.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.knet.order.mapper.SysOrderGroupMapper;
import com.knet.order.model.entity.SysOrderGroup;
import com.knet.order.service.ISysOrderGroupService;
import com.knet.order.system.event.OrderCreatedEvent;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025-03-11 15:45:02
 * @description: 针对表【sys_order(B2B订单主表)】的数据库操作Service实现
 */
@Service
public class SysOrderGroupServiceImpl extends ServiceImpl<SysOrderGroupMapper, SysOrderGroup> implements ISysOrderGroupService {
    @Resource
    private ApplicationEventPublisher eventPublisher;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createOrder(SysOrderGroup sysOrderGroup) {
        // 保存订单信息
        this.save(sysOrderGroup);
        // 注册事务同步回调
        OrderCreatedEvent event = new OrderCreatedEvent(this, sysOrderGroup);
        pushOrderCreateEvent(event);
    }

    /**
     * 发送订单创建事件
     *
     * @param orderCreatedEvent 订单创建事件
     */
    public void pushOrderCreateEvent(OrderCreatedEvent orderCreatedEvent) {
        eventPublisher.publishEvent(orderCreatedEvent);
    }
}

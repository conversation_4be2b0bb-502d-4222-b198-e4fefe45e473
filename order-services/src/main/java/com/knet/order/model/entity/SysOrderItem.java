package com.knet.order.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.knet.common.base.BaseWithNotIdEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/3/7 14:15
 * @description: 订单商品明细
 */

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(name = "sys_order_item", description = "订单商品明细")
@TableName("sys_order_item")
public class SysOrderItem extends BaseWithNotIdEntity {
    /**
     * 明细项ID
     */
    @TableId(type = IdType.AUTO)
    private Long itemId;

    /**
     * 外键关联订单
     */
    @Schema(description = "外键关联订单")
    private String orderId;

    /**
     * sku
     */
    @Schema(description = "sku")
    private String sku;

    /**
     * 尺码
     */
    @Schema(description = "尺码")
    private String size;

    /**
     * 品名
     */
    @Schema(description = "品名")
    private String name;

    /**
     * 单价（含税，单位：美元）
     */
    @Schema(description = " 单价（含税，单位：美元）")
    private BigDecimal price;

    /**
     * KG oneId
     */
    @Schema(description = "KG oneId")
    private String oneId;

    /**
     * b2b 商品唯一id
     */
    @Schema(description = "b2b 商品唯一id")
    private String knetListingId;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer count;
}
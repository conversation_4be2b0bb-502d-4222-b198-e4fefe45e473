package com.knet.order.system.interceptor;

import cn.hutool.core.date.StopWatch;
import com.knet.common.annotation.Loggable;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2025/3/6 11:48
 * @description: 请求响应日志切面
 */
@Slf4j
@Aspect
@Component
public class LoggableAspect {
    @Around("@annotation(loggable)")
    public Object logAround(ProceedingJoinPoint joinPoint, Loggable loggable) throws Throwable {
        StopWatch stopWatch = new StopWatch("多阶段任务");
        // 获取方法签名和注解信息
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        String description = loggable.value();
        // 获取请求信息
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        assert attributes != null;
        HttpServletRequest request = attributes.getRequest();
        // 记录请求日志
        log.info("URL: {}", request.getRequestURL().toString());
        log.info("HTTP Method: {}", request.getMethod());
        log.info("Class Method: {}.{}", method.getDeclaringClass().getName(), method.getName());
        log.info("Description: {}", description);
        log.info("Request Args: {}", Arrays.toString(joinPoint.getArgs()));
        // 执行目标方法并记录耗时
        stopWatch.start();
        Object result = joinPoint.proceed();
        stopWatch.stop();
        // 记录响应日志
        log.info("Response Args: {}", result);
        log.info("Time-Consuming: {} ms", stopWatch.getTotalTimeMillis());
        return result;
    }
}

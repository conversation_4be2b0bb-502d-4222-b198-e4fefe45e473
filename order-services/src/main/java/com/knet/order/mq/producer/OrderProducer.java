package com.knet.order.mq.producer;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.RandomUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageDeliveryMode;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

import static com.knet.common.constants.OrderServicesConstants.KNET_B2B_ORDER_MESSAGE_PREFIX;

/**
 * <AUTHOR>
 * @date 2025/3/19 13:31
 * @description: 订单生产者
 */
@Slf4j
@Component
public class OrderProducer {

    @Resource
    private RabbitTemplate rabbitTemplate;

    public void sendOrderCreateEvent(String messageBody) {
        String messageId = String.format(KNET_B2B_ORDER_MESSAGE_PREFIX, RandomUtil.randomString(16));
        // 构建消息属性
        MessageProperties properties = new MessageProperties();
        properties.setHeader("routingKey", "order.create");
        properties.setHeader("messageId", messageId);
        properties.setDeliveryMode(MessageDeliveryMode.PERSISTENT);
        Message message = new Message(messageBody.getBytes(StandardCharsets.UTF_8), properties);
        // 发送消息（使用确认回调）
        CorrelationData correlationData = new CorrelationData(messageId);
        rabbitTemplate.convertAndSend(
                "order-exchange",
                "order.create",
                message,
                correlationData
        );
        correlationData.getFuture().addCallback(
                result -> {
                    assert result != null;
                    if (BeanUtil.isNotEmpty(result) && result.isAck()) {
                        log.info("消息到达Broker: {}", messageId);
                    } else {
                        log.error("消息未到达Broker: {}", messageId);
                    }
                },
                ex -> {
                    //消息补偿机制，记录消息发送失败的消息，重试发送
                    log.error("消息发送异常: {}", ex.getMessage());
                }
        );
    }
}

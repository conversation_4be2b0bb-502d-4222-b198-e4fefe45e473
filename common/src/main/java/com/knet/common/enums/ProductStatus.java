package com.knet.common.enums;

import com.alibaba.fastjson2.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/2/17 10:16
 * @description: 商品状态
 */
@Getter
@AllArgsConstructor
public enum ProductStatus {
    /**
     * 上架
     */
    ON_SALE("onSale"),
    /**
     * 下架
     */
    OFF_SALE("offSale");

    @EnumValue
    @JSONField
    @JsonValue
    private String description;

    public String getDescription() {
        return description;
    }

    @Override
    public String toString() {
        return this.description;
    }
}

package com.knet.payment.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.knet.payment.mapper.SysPaymentFlowMapper;
import com.knet.payment.model.entity.SysPaymentFlow;
import com.knet.payment.service.ISysPaymentFlowService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025-03-12 13:37:21
 * @description: 针对表【sys_payment_flow(支付流水表)】的数据库操作Service实现
 */
@Service
public class SysPaymentFlowServiceImpl extends ServiceImpl<SysPaymentFlowMapper, SysPaymentFlow>
        implements ISysPaymentFlowService {
}





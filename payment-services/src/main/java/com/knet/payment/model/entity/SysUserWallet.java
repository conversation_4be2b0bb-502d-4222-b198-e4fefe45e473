package com.knet.payment.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.knet.common.base.BaseEntity;
import com.knet.common.utils.NumberUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/3/12 14:15
 * @description: 用户钱包表
 */
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(name = "sys_user_wallet", description = "用户钱包表")
@TableName("sys_user_wallet")
public class SysUserWallet extends BaseEntity {
    /**
     * 用户ID（业务唯一标识）
     */
    @Schema(description = "用户ID（业务唯一标识）")
    private Long userId;

    /**
     * 可用余额
     */
    @Schema(description = "可用余额 美元", example = "100.00")
    private BigDecimal balance;

    /**
     * 冻结金额 用于处理支付中的预扣款
     */
    @Schema(description = "冻结金额 美元", example = "100.00")
    private BigDecimal frozenBalance;

    public SysUserWallet(Long userId, BigDecimal amount, BigDecimal zero) {
        this.userId = userId;
        this.balance = amount;
        this.frozenBalance = zero;
    }

    public String getFormattedBalanceStr() {
        BigDecimal balance = this.getBalance() != null ? this.getBalance() : BigDecimal.ZERO;
        return NumberUtils.formatDecimal(balance);
    }

    public String getFormattedFrozenBalanceStr() {
        BigDecimal frozenBalance = this.getFrozenBalance() != null ? this.getFrozenBalance() : BigDecimal.ZERO;
        return NumberUtils.formatDecimal(frozenBalance);
    }

    public String getFormattedTotalBalanceStr() {
        BigDecimal balance = this.getBalance() != null ? this.getBalance() : BigDecimal.ZERO;
        BigDecimal frozenBalance = this.getFrozenBalance() != null ? this.getFrozenBalance() : BigDecimal.ZERO;
        return NumberUtils.formatDecimal(balance.add(frozenBalance));
    }
}